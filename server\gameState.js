// server/gameState.js

const { v4: uuidv4 } = require('uuid');

const MAP_WIDTH = 17;
const MAP_HEIGHT = 15;
const TILE_SIZE = 32;
const SPAWN_POSITIONS = [
  { row: 0, col: 0 },
  { row: 0, col: MAP_WIDTH - 1 },
  { row: MAP_HEIGHT - 1, col: 0 },
  { row: MAP_HEIGHT - 1, col: MAP_WIDTH - 1 },
];

const POWERUP_TYPES = ['bomb_up', 'flame_up', 'speed_up'];

function randomPowerupType() {
  return POWERUP_TYPES[Math.floor(Math.random() * POWERUP_TYPES.length)];
}

function generateMap() {
  // Similar to client
  const map = [];
  for (let row = 0; row < MAP_HEIGHT; row++) {
    const currentRow = [];
    for (let col = 0; col < MAP_WIDTH; col++) {
      // Always empty for spawn corners and their adjacent tiles
      if (
        (row < 2 && col < 2) ||
        (row < 2 && col > MAP_WIDTH - 3) ||
        (row > MAP_HEIGHT - 3 && col < 2) ||
        (row > MAP_HEIGHT - 3 && col > MAP_WIDTH - 3) ||
        (row === 2 && (col === 0 || col === MAP_WIDTH - 1)) ||
        (row === 0 && (col === 2 || col === MAP_WIDTH - 3)) ||
        (row === MAP_HEIGHT - 3 && (col === 0 || col === MAP_WIDTH - 1)) ||
        (row === MAP_HEIGHT - 1 && (col === 2 || col === MAP_WIDTH - 3))
      ) {
        currentRow.push('E');
        continue;
      }
      if (row % 2 === 1 && col % 2 === 1) {
        currentRow.push('W');
        continue;
      }
      currentRow.push(Math.random() < 0.6 ? 'B' : 'E');
    }
    map.push(currentRow);
  }
  return map;
}

class Player {
  constructor(id, nickname, startRow, startCol) {
    this.id = id;
    this.nickname = nickname;
    this.row = startRow;
    this.col = startCol;
    this.lives = 3;
    this.speed = 1;
    this.bombsCount = 1;
    this.flameRange = 1;
    this.alive = true;
  }
}

class Bomb {
  constructor(row, col, ownerId, flameRange) {
    this.row = row;
    this.col = col;
    this.ownerId = ownerId;
    this.flameRange = flameRange;
    this.timer = 3000;
    this.exploded = false;
    this.startTime = Date.now();
  }
}

class PowerUp {
  constructor(row, col, type) {
    this.row = row;
    this.col = col;
    this.type = type;
    this.collected = false;
  }
}

class GameState {
  constructor() {
    this.map = generateMap();
    this.players = [];
    this.bombs = [];
    this.explosions = [];
    this.powerups = [];
    this.chat = [];
    this.phase = 'lobby';
    this.winner = null;
    this.lastUpdate = Date.now();
    this.countdown = 10;
    this.lobbyStart = Date.now();
    this._lastPlayerCount = undefined; // Initialize for tracking player count changes
  }

  addPlayer(nickname) {
    console.log(`[GameState] addPlayer called with nickname: ${nickname}`);
    if (this.players.length >= 4) return null;
    // Find the first available spawn position
    const usedPositions = this.players.map(p => `${p.row},${p.col}`);
    console.log(`[GameState] usedPositions:`, usedPositions);
    let pos = null;
    for (const candidate of SPAWN_POSITIONS) {
      if (!usedPositions.includes(`${candidate.row},${candidate.col}`)) {
        pos = candidate;
        break;
      }
    }
    if (!pos) {
      console.error('[GameState] No available spawn position for new player!');
      return null;
    }
    const id = uuidv4();
    const player = new Player(id, nickname, pos.row, pos.col);
    this.players.push(player);
    console.log(`[GameState] Player added: ${nickname} (${id}) at (${pos.row},${pos.col})`);
    console.log(`[GameState] Current players:`, this.players.map(p => `${p.nickname} (${p.id})`).join(', '));
    return player;
  }

  removePlayer(id) {
    const player = this.players.find(p => p.id === id);
    if (player) {
      console.log(`[GameState] Removing player: ${player.nickname} (${id})`);
    }
    this.players = this.players.filter(p => p.id !== id);
    console.log(`[GameState] Current players:`, this.players.map(p => `${p.nickname} (${p.id})`).join(', '));
  }

  handleAction(id, action) {
    const player = this.players.find(p => p.id === id);
    console.log(`[DEBUG] handleAction: id=${id}, action=${JSON.stringify(action)}, phase=${this.phase}, player=${player ? player.nickname : 'null'}`);
    if (!player || !player.alive || this.phase !== 'game') return;

    if (action.type === 'move') {
      // Handle movement with proper speed mechanics - move one tile at a time
      // Speed affects movement frequency, not distance per move
      let { row, col } = player;
      let newRow = row;
      let newCol = col;

      switch (action.dir) {
        case 'up': newRow = row - 1; break;
        case 'down': newRow = row + 1; break;
        case 'left': newCol = col - 1; break;
        case 'right': newCol = col + 1; break;
      }

      // Check boundaries
      if (newRow < 0 || newRow >= MAP_HEIGHT || newCol < 0 || newCol >= MAP_WIDTH) return;

      // Check walkable
      if (this.map[newRow][newCol] !== 'E') return;

      // Prevent walking onto a bomb
      if (this.bombs.some(b => !b.exploded && b.row === newRow && b.col === newCol)) return;

      // Move is valid
      player.row = newRow;
      player.col = newCol;

      // Check for powerup pickup
      this.powerups.forEach(powerup => {
        if (!powerup.collected && powerup.row === newRow && powerup.col === newCol) {
          console.log(`[GameState] Player ${player.nickname} collected ${powerup.type}`);
          switch (powerup.type) {
            case 'bomb_up':
              player.bombsCount = Math.min(player.bombsCount + 1, 8); // Cap at 8 bombs
              break;
            case 'flame_up':
              player.flameRange = Math.min(player.flameRange + 1, 10); // Cap at 10 range
              break;
            case 'speed_up':
              player.speed = Math.min(player.speed + 1, 5); // Cap at 5 speed
              break;
          }
          powerup.collected = true;
        }
      });
    } else if (action.type === 'bomb') {
      const activeBombs = this.bombs.filter(b => b.ownerId === player.id && !b.exploded);
      if (activeBombs.length >= player.bombsCount) return;
      if (this.bombs.some(b => b.row === player.row && b.col === player.col && !b.exploded)) return;

      console.log(`[GameState] Player ${player.nickname} placed bomb at (${player.row}, ${player.col})`);
      this.bombs.push(new Bomb(player.row, player.col, player.id, player.flameRange));
    } else if (action.type === 'restart') {
      // Only allow restart if game is ended and player was in the game
      if (this.phase === 'end') {
        console.log(`[GameState] Player ${player.nickname} requested restart`);
        this.restartGame();
      }
    } else if (action.type === 'lobby') {
      // Return to lobby
      if (this.phase === 'end') {
        console.log(`[GameState] Player ${player.nickname} requested return to lobby`);
        this.returnToLobby();
      }
    }
  }

  handleChat(id, text) {
    const player = this.players.find(p => p.id === id);
    if (!player) return;
    this.chat.push({ nickname: player.nickname, text });
    if (this.chat.length > 50) this.chat.shift();
  }

  update() {
    const now = Date.now();
    // Lobby logic
    if (this.phase === 'lobby') {
      // If all players left, reset lobby timer
      if (this.players.length === 0) {
        this.lobbyStart = now;
        this.countdown = 10;
        return;
      }

      // If player count goes from 1 to 2, reset lobbyStart for 20s wait
      if (this._lastPlayerCount !== undefined && this._lastPlayerCount === 1 && this.players.length === 2) {
        this.lobbyStart = now;
        this.countdown = 10;
      }
      this._lastPlayerCount = this.players.length;

      // Game start logic based on requirements:
      // - With 2+ players: wait 20s, then 10s countdown
      // - With 4 players: immediate 10s countdown
      const timeSinceLobbyStart = now - this.lobbyStart;

      if (this.players.length >= 2) {
        if (this.players.length === 4) {
          // 4 players: immediate countdown
          if (this.countdown > 0) {
            this.countdown = Math.max(0, 10 - Math.floor(timeSinceLobbyStart / 1000));
            if (this.countdown === 0) {
              this.startGame();
            }
          }
        } else {
          // 2-3 players: 20s wait, then 10s countdown
          if (timeSinceLobbyStart >= 20000) {
            // Start countdown after 20s
            this.countdown = Math.max(0, 10 - Math.floor((timeSinceLobbyStart - 20000) / 1000));
            if (this.countdown === 0) {
              this.startGame();
            }
          } else {
            // Still in 20s wait period
            this.countdown = 10;
          }
        }
      }
      return;
    }
    // Game logic
    // Update bombs
    this.bombs.forEach(bomb => {
      if (!bomb.exploded && now - bomb.startTime >= bomb.timer) {
        this.handleExplosion(bomb);
      }
    });
    // Remove expired explosions
    this.explosions = this.explosions.filter(ex => ex.expiresAt > now);

    // Win condition - check for game end
    const alive = this.players.filter(p => p.alive);
    if (alive.length <= 1 && !this.winner && this.players.length > 1) {
      if (alive.length === 1) {
        this.winner = alive[0].nickname;
        console.log(`[GameState] Game won by: ${this.winner}`);
      } else {
        this.winner = 'Draw'; // All players died
        console.log(`[GameState] Game ended in a draw`);
      }
      this.phase = 'end';
      this.gameEndTime = now;
    }
  }

  handleExplosion(bomb) {
     if (bomb.exploded) return;
    bomb.exploded = true;
    const affected = [{ row: bomb.row, col: bomb.col }];
    const directions = [
      { dr: -1, dc: 0 },
      { dr: 1, dc: 0 },
      { dr: 0, dc: -1 },
      { dr: 0, dc: 1 },
    ];
    for (const { dr, dc } of directions) {
      for (let i = 1; i <= bomb.flameRange; i++) {
        const r = bomb.row + dr * i;
        const c = bomb.col + dc * i;
        if (r < 0 || r >= MAP_HEIGHT || c < 0 || c >= MAP_WIDTH) break;
        if (this.map[r][c] === 'W') break;
        affected.push({ row: r, col: c });
        if (this.map[r][c] === 'B') break;
      }
    }
    const now = Date.now();
    affected.forEach(({ row, col }) => {
      this.explosions.push({ row, col, expiresAt: now + 500 });
    });
    // Destroy blocks, maybe spawn powerups
    affected.forEach(({ row, col }) => {
      if (this.map[row][col] === 'B') {
        this.map[row][col] = 'E';
        if (Math.random() < 0.3) {
          this.powerups.push(new PowerUp(row, col, randomPowerupType()));
        }
      }
    });
    // Damage players
    this.players.forEach(player => {
      if (!player.alive) return;
      if (affected.some(a => a.row === player.row && a.col === player.col)) {
        player.lives--;
        if (player.lives <= 0) player.alive = false;
      }
    });
    // Chain reaction
    this.bombs.forEach(b => {
      if (!b.exploded && affected.some(a => a.row === b.row && a.col === b.col)) {
        b.exploded = true;
        this.handleExplosion(b);
      }
    });
    // bomb.exploded = true;
  }

  startGame() {
    console.log('[GameState] Starting game with', this.players.length, 'players');
    this.phase = 'game';
    this.lastUpdate = Date.now();
    this.map = generateMap();
    this.bombs = [];
    this.explosions = [];
    this.powerups = [];
    this.winner = null;
    this.gameEndTime = null;

    // Reset player positions and lives
    const positions = [...SPAWN_POSITIONS];
    for (let i = 0; i < this.players.length; i++) {
      const p = this.players[i];
      const pos = positions[i % positions.length];
      p.row = pos.row;
      p.col = pos.col;
      p.lives = 3;
      p.speed = 1;
      p.bombsCount = 1;
      p.flameRange = 1;
      p.alive = true;
    }
  }

  restartGame() {
    console.log('[GameState] Restarting game');
    if (this.players.length >= 2) {
      this.startGame();
    } else {
      console.log('[GameState] Not enough players to restart, returning to lobby');
      this.returnToLobby();
    }
  }

  returnToLobby() {
    console.log('[GameState] Returning to lobby');
    this.phase = 'lobby';
    this.lobbyStart = Date.now();
    this.countdown = 10;
    this.winner = null;
    this.gameEndTime = null;
    this.map = [];
    this.bombs = [];
    this.explosions = [];
    this.powerups = [];

    // Keep players but reset their game state
    this.players.forEach(p => {
      p.lives = 3;
      p.speed = 1;
      p.bombsCount = 1;
      p.flameRange = 1;
      p.alive = true;
    });
  }

  getState() {
    let lobbyCountdown = 0;
    let waitingTime = 0;

    if (this.phase === 'lobby') {
      const timeSinceLobbyStart = Date.now() - this.lobbyStart;

      if (this.players.length >= 2) {
        if (this.players.length === 4) {
          // 4 players: immediate countdown
          lobbyCountdown = this.countdown;
        } else {
          // 2-3 players: show waiting time or countdown
          if (timeSinceLobbyStart < 20000) {
            waitingTime = Math.max(0, 20 - Math.floor(timeSinceLobbyStart / 1000));
            lobbyCountdown = 0;
          } else {
            lobbyCountdown = this.countdown;
            waitingTime = 0;
          }
        }
      }
    }
    return {
      map: this.map,
      players: this.players.map(p => ({
        id: p.id,
        nickname: p.nickname,
        row: p.row,
        col: p.col,
        lives: p.lives,
        speed: p.speed,
        bombsCount: p.bombsCount,
        flameRange: p.flameRange,
        alive: p.alive,
      })),
      bombs: this.bombs.map(b => ({
        row: b.row,
        col: b.col,
        ownerId: b.ownerId,
        flameRange: b.flameRange,
        exploded: b.exploded,
      })),
      explosions: this.explosions.map(e => ({ row: e.row, col: e.col })),
      powerups: this.powerups.filter(p => !p.collected).map(p => ({
        row: p.row,
        col: p.col,
        type: p.type,
        collected: p.collected,
      })),
      chat: this.chat,
      phase: this.phase,
      winner: this.winner,
      gameEndTime: this.gameEndTime,
      lobbyCountdown,
      waitingTime,
    };
  }
}

module.exports = GameState;
