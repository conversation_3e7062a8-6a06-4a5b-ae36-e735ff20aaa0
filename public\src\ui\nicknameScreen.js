// nicknameScreen.js
import { renderDOM } from '../../msec/framework/dom.js';
import { setState } from '../../msec/framework/state.js';
import { navigate } from '../../msec/framework/router.js';
import { send } from '../multiplayer/socket.js';

export function nicknameScreen() {
  const root = document.getElementById('app');
  const vdom = {
    tag: 'div',
    attrs: { class: 'nickname-form' },
    children: [
      {tag: 'div', attrs: { class: 'character-container' }, children: [
        {tag: 'img', attrs: { src: '../static/images/character_header.png', alt: 'Bomberman Logo', class: 'img-header' }, children: []},
      ]},
      {tag: 'div', attrs: { class: 'nickname-container' }, children: [
      {tag: 'div', attrs: { class: 'nickname-form' }, children: [
      { tag: 'h2', attrs: {}, children: ['Enter your nickname'] },
      {
        tag: 'form',
        attrs: { id: 'nickname-form', autocomplete: 'off' },
        children: [
          {
            tag: 'input',
            attrs: {
              type: 'text',
              id: 'nickname-input',
              placeholder: 'Nickname',
              maxlength: 16,
              required: true,
              autofocus: true,
            },
            children: [],
          },
          {
            tag: 'button',
            attrs: { type: 'submit' },
            children: ['Join'],
          },
        ],
      },
      {
        tag: 'div',
        attrs: { class: 'links-demo', style: 'margin-top: 50px; font-size: 14px;' },
        children: [
          // { tag: 'p', attrs: {}, children: ['Test links:'] },
          {
            tag: 'a',
            attrs: { href: '/lobby', style: 'margin-right: 10px;' },
            children: ['Go to Lobby (Internal)']
          },
          {
            tag: 'a',
            attrs: { href: 'https://github.com', style: 'margin-right: 10px;' },
            children: ['GitHub (External)']
          },
          {
            tag: 'a',
            attrs: { href: 'https://developer.mozilla.org'},
            children: ['MDN (External)']
          },
        ],
      },
      ]},
    ]},
  ]
  };
  renderDOM(vdom, root);

  document.getElementById('nickname-form').onsubmit = (e) => {
    e.preventDefault();
    const nickname = document.getElementById('nickname-input').value.trim();
    if (!nickname) return;
    setState({ nickname });
    send({ type: 'nickname', nickname });
    navigate('/lobby');
  };
}
