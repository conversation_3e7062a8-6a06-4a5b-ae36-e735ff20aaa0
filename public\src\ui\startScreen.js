// nicknameScreen.js
import { renderDOM } from '../../msec/framework/dom.js';
import { setState } from '../../msec/framework/state.js';
import { navigate } from '../../msec/framework/router.js';
import { send } from '../multiplayer/socket.js';

export function StartScreen() {
  const root = document.getElementById('app');
  console.log('Rendering StartScreen');
  const vdom = {
    tag: 'div',
    attrs: { class: 'splash-container' },
    children: [
      {
        tag: 'div',
        attrs: { class: 'splash-screen' },
        children: [
          {
            tag: 'div',
            attrs: { class: 'logo-container' },
            children: [
              {
                tag: 'img',
                attrs: {
                  src: '../static/images/logo.png',
                  alt: 'Bomberman Logo',
                  class: 'logo',
                },
                children: [],
              },
            ],
          },
          {
            tag: 'div',
            attrs: { class: 'menu' },
            children: [
              {
                tag: 'a',
                attrs: { href: '/player' },
                children: ['Start Game'],
              },
              {
                tag: 'a',
                attrs: { href: '#' },
                children: ['exit'],
              },
            ],
          },
        ],
      },
    ],
  };

  renderDOM(vdom, root);
}

